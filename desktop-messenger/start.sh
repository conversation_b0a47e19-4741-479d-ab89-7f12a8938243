#!/bin/bash

# Social Network Messenger Startup Script
# This script starts the messenger as a web application

echo "🚀 Starting Social Network Messenger..."
echo "📱 Web version - running in browser"
echo ""

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed."
    echo "Please install Python 3 and try again."
    exit 1
fi

# Check if backend is running
echo "🔍 Checking if backend is running on localhost:8080..."
if curl -s http://localhost:8080/api/health > /dev/null 2>&1; then
    echo "✅ Backend is running"
else
    echo "⚠️  Warning: Backend doesn't seem to be running on localhost:8080"
    echo "   Make sure your Social Network backend is started before using the messenger"
    echo ""
fi

# Start the web server
echo "🌐 Starting web server on http://localhost:8081..."
echo "📂 Serving files from: $(pwd)/renderer"
echo ""
echo "🎉 Messenger will open in your browser shortly..."
echo "💡 Press Ctrl+C to stop the server"
echo ""

# Start server and open browser
python3 -m http.server 8081 --directory renderer &
SERVER_PID=$!

# Wait a moment for server to start
sleep 2

# Try to open in browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:8081
elif command -v open &> /dev/null; then
    open http://localhost:8081
elif command -v start &> /dev/null; then
    start http://localhost:8081
else
    echo "🌐 Please open http://localhost:8081 in your browser"
fi

# Wait for server process
wait $SERVER_PID
