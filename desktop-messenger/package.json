{"name": "social-network-messenger", "version": "1.0.0", "description": "Cross-platform desktop messenger for Social Network", "main": "renderer/index.html", "scripts": {"start": "python3 -m http.server 8081 --directory renderer", "dev": "python3 -m http.server 8081 --directory renderer"}, "keywords": ["messenger", "chat", "social-network", "web-app"], "author": "Social Network Team", "license": "MIT", "dependencies": {}, "build": {"appId": "com.socialnetwork.messenger", "productName": "Social Network Messenger", "directories": {"output": "dist"}, "files": ["main/**/*", "preload/**/*", "renderer/**/*", "assets/**/*", "package.json"], "mac": {"category": "public.app-category.social-networking", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}