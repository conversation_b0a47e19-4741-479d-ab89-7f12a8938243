@echo off
REM Social Network Messenger Startup Script for Windows
REM This script starts the messenger as a web application

echo 🚀 Starting Social Network Messenger...
echo 📱 Web version - running in browser
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Python is required but not installed.
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if backend is running
echo 🔍 Checking if backend is running on localhost:8080...
curl -s http://localhost:8080/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is running
) else (
    echo ⚠️  Warning: Backend doesn't seem to be running on localhost:8080
    echo    Make sure your Social Network backend is started before using the messenger
    echo.
)

REM Start the web server
echo 🌐 Starting web server on http://localhost:8081...
echo 📂 Serving files from: %cd%\renderer
echo.
echo 🎉 Messenger will open in your browser shortly...
echo 💡 Press Ctrl+C to stop the server
echo.

REM Start server and open browser
start /b python -m http.server 8081 --directory renderer

REM Wait a moment for server to start
timeout /t 2 /nobreak >nul

REM Open in browser
start http://localhost:8081

REM Keep window open
echo Press any key to stop the server...
pause >nul

REM Kill the Python server process
taskkill /f /im python.exe >nul 2>&1
